import type { CreateUpdateJettyRequestDto, RemoteServiceErrorResponse, VesselHeaderDto } from '@/client';
import { postApiIdjasJettyRequest } from '@/client/sdk.gen';
import ApplicationForm from '@/components/applications/application-form';
import { Button } from '@/components/ui/button';
import { DocumentPreviewDialog } from '@/components/ui/DocumentPreviewDialog';
import { formatDateForInput } from '@/lib/date-helper';
import { useToast } from '@/lib/useToast';
import { HotTable } from '@handsontable/react-wrapper';
import { router } from '@inertiajs/react';
import { useMutation } from '@tanstack/react-query';
import { registerAllModules } from 'handsontable/registry';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-horizon.css';
import 'handsontable/styles/ht-theme-main.min.css';
import { useRef, useState } from 'react';
import { columns, type TableRowData } from './handsontable-column';
import { renderDeleteButton, renderPreviewButton, renderSubmitButton } from './handsontable-renderer';

registerAllModules();

const initialData: TableRowData[] = [];

export const JettyRequestCreate = () => {
  const { toast } = useToast()
  const [docNum, setDocNum] = useState('25060001');
  const [vesselType, setVesselType] = useState('');
  const [voyage, setVoyage] = useState('001');
  const [jetty, setJetty] = useState('');
  const [arrivalDate, setArrivalDate] = useState('');
  const [departureDate, setDepartureDate] = useState('');
  const [asideDate, setAsideDate] = useState('');
  const [castOfDate, setCastOfDate] = useState('');
  const [postDate, setPostDate] = useState('');
  const [portOrigin, setPortOrigin] = useState('');
  const [destinationPort, setDestinationPort] = useState('');
  const [barge, setBarge] = useState('');
  const [tableData, setTableData] = useState(initialData);
  const [vessel, setVessel] = useState<VesselHeaderDto | null>(null);

  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewDocumentSrc, setPreviewDocumentSrc] = useState('');
  const [loadingStates, setLoadingStates] = useState<Map<number, boolean>>(new Map());

  const hotTableComponent = useRef(null);

  // Helper functions for preview dialog
  const handlePreview = (documentSrc: string) => {
    setPreviewDocumentSrc(documentSrc);
    setIsPreviewDialogOpen(true);
  };

  const setLoadingState = (row: number, loading: boolean) => {
    setLoadingStates(prev => {
      const newMap = new Map(prev);
      if (loading) {
        newMap.set(row, true);
      } else {
        newMap.delete(row);
      }
      return newMap;
    });
  };

  const createJettyRequestMutation = useMutation({
    mutationFn: async (payload: CreateUpdateJettyRequestDto) => 
      postApiIdjasJettyRequest({ body: payload }),
    onSuccess: (data) => {
      toast({
        title: 'Success',
        description: 'Jetty request saved successfully.',
        variant: 'success',
      });
      setTableData([]);
      setVessel(null);
      setVoyage('');
      setJetty('');
      setArrivalDate('');
      setDepartureDate('');
      setAsideDate('');
      setCastOfDate('');
      setPostDate('');
      if (data && data.data?.id) {
        router.visit(`/application/${data.data?.id}/edit`);
      }
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err?.error?.message || 'Error',
        description: err?.error?.details || 'Failed to save jetty request.',
        variant: 'destructive',
      });
    }
  });
  

  const handleSave = () => {
    // Prevent duplicate submissions
    if (createJettyRequestMutation.isPending) {
      return;
    }

    const payload = {
      docNum: Number(docNum),
      vesselType,
      vesselName: vessel?.vesselName ?? '',
      voyage,
      jetty,
      arrivalDate,
      departureDate,
      asideDate,
      castOfDate,
      postDate,
      portOrigin,
      destinationPort,
      barge,
      referenceId: vessel?.id,
      items: tableData.map(item => ({
        tenantName: item.tenantName,
        itemName: item.itemName,
        qty: Number(item.quantity) || 0,
        uoM: item.uom,
        notes: item.remark,
        letterNo: item.letterNo,
        letterDate: item.letterDate,
        id: item.id,
        status: 1 as const, // Open status
      })),
    };
    createJettyRequestMutation.mutate(payload);
  };

  const handleVesselChange = (selectedVessel: VesselHeaderDto | null) => {
    setVessel(selectedVessel);
    if (selectedVessel) {
      setVoyage(selectedVessel.voyage || '');
      setJetty(selectedVessel.jetty?.id || '');
      setArrivalDate(formatDateForInput(selectedVessel.vesselArrival));
      setDepartureDate(formatDateForInput(selectedVessel.vesselDeparture));
      setPortOrigin(selectedVessel.portOrigin || '');
      setDestinationPort(selectedVessel.destinationPort || '');
      setBarge(selectedVessel.barge?.name || '');
      if (selectedVessel.items) {
        setTableData(
          selectedVessel.items.map(item => ({
            tenantName: item.tenant?.name ?? '',
            itemName: item.itemName || '',
            quantity: String(item.itemQty ?? ''),
            uom: item.unitQty || '',
            remark: item.remarks || '',
            status: 'Draft',
            letterNo: item.letterNo || '',
            letterDate: item.letterDate || '',
            id: item.id,
            preview: '',
            submit: '',
            delete: '',
          }))
        );
      } else {
        setTableData([]);
      }
    } else {
      setVoyage('');
      setJetty('');
      setArrivalDate('');
      setDepartureDate('');
      setAsideDate('');
      setCastOfDate('');
      setPostDate('');
      setTableData([]);
    }
  };

  const columnConfig = columns.map(col => {
    if (col.data === 'id') {
      return { ...col, renderer: renderPreviewButton(tableData, handlePreview, loadingStates, setLoadingState) };
    }
    if (col.data === 'submit') {
      return { ...col, renderer: renderSubmitButton(tableData, vesselType, loadingStates, setLoadingState) };
    }
    if (col.data === 'delete') {
      return { ...col, renderer: renderDeleteButton(tableData, setTableData, loadingStates, setLoadingState) };
    }
    return col;
  });

  return (
    <div className="container mx-auto">
      <div className='bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4'>
        <ApplicationForm
          docNum={docNum}
          vesselType={vesselType}
          vessel={vessel}
          voyage={voyage}
          jetty={jetty}
          arrivalDate={arrivalDate}
          departureDate={departureDate}
          asideDate={asideDate}
          castOfDate={castOfDate}
          postDate={postDate}
          portOrigin={portOrigin}
          destinationPort={destinationPort}
          barge={barge}
          onDocNumChange={setDocNum}
          onVesselTypeChange={setVesselType}
          onVesselChange={handleVesselChange}
          onVoyageChange={setVoyage}
          onJettyChange={setJetty}
          onArrivalDateChange={setArrivalDate}
          onDepartureDateChange={setDepartureDate}
          onAsideDateChange={setAsideDate}
          onCastOfDateChange={setCastOfDate}
          onPostDateChange={setPostDate}
          onPortOriginChange={setPortOrigin}
          onDestinationPortChange={setDestinationPort}
          onBargeChange={setBarge}
        />
        <div  style={{ maxWidth: '100%', overflowX: 'auto' }} className="mb-8">
          <HotTable
            ref={hotTableComponent}
            themeName="ht-theme-main"
            data={tableData}
            columns={columnConfig}
            colHeaders={columns.map(col => col.title)}
            rowHeaders={true}
            height="50vh"
            rowHeights={27}
            currentRowClassName="currentRow"
            currentColClassName="currentCol"
            // autoWrapRow={true}
            licenseKey="non-commercial-and-evaluation"
            // stretchH="all"
            contextMenu={true}
            manualColumnResize={true}
            manualRowResize={true}
            autoColumnSize={false}
            autoRowSize={false}
            startRows={1}
            viewportRowRenderingOffset={1000}
            viewportColumnRenderingOffset={100}
            dropdownMenu={true}
            filters={true}
            colWidths={80}
            width="100%"
            persistentState={true}
          />
        </div>
        <div className="flex justify-end">
          <Button 
            onClick={handleSave} 
            disabled={createJettyRequestMutation.isPending}
            className="px-6 py-2 text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {createJettyRequestMutation.isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>
      <DocumentPreviewDialog
        isOpen={isPreviewDialogOpen}
        onOpenChange={setIsPreviewDialogOpen}
        documentSrc={previewDocumentSrc}
      />
    </div>
  );
};