using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.JettyApproval.Migrations
{
    /// <inheritdoc />
    public partial class FixApprovalStageStatusDataType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Fix any existing string values in Status column to proper integer values
            // This handles cases where Status might have been stored as string instead of int
            migrationBuilder.Sql(@"
                UPDATE ApprovalStages
                SET Status = CASE
                    WHEN TRY_CAST(Status AS INT) IS NOT NULL THEN Status
                    WHEN Status = 'Draft' OR Status = '0' THEN 0
                    WHEN Status = 'Pending' OR Status = '1' THEN 1
                    WHEN Status = 'Approved' OR Status = '2' THEN 2
                    WHEN Status = 'Rejected' OR Status = '3' THEN 3
                    WHEN Status = 'Cancelled' OR Status = '4' THEN 4
                    WHEN Status = 'Waiting' OR Status = '5' THEN 5
                    ELSE 0
                END
                WHERE TRY_CAST(Status AS INT) IS NULL OR Status IN ('Pending', 'Approved', 'Rejected', 'Cancelled')
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
