using Imip.JettyApproval.Approvals.ApprovalStages;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.JettyRequests;
using Imip.JettyApproval.Mapping.Mappers;
using Imip.JettyApproval.Models;
using Imip.JettyApproval.Services;

using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Users;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// Application service for ApprovalStage entity
/// </summary>
public class ApprovalStageAppService :
    CrudAppService<ApprovalStage, ApprovalStageDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalStageDto, CreateUpdateApprovalStageDto>,
    IApprovalStageAppService
{
    private readonly IApprovalStageRepository _approvalStageRepository;
    private readonly IApprovalTemplateRepository _approvalTemplateRepository;
    private readonly IApprovalCriteriaRepository _approvalCriteriaRepository;
    private readonly IApprovalApproverRepository _approvalApproverRepository;
    private readonly IJettyRequestItemRepository _jettyRequestItemRepository;
    private readonly ApprovalStageMapper _mapper;
    private readonly ILogger<ApprovalStageAppService> _logger;

    public ApprovalStageAppService(
        IApprovalStageRepository approvalStageRepository,
        IApprovalTemplateRepository approvalTemplateRepository,
        IApprovalCriteriaRepository approvalCriteriaRepository,
        IApprovalApproverRepository approvalApproverRepository,
        IJettyRequestItemRepository jettyRequestItemRepository,
        ApprovalStageMapper mapper,
        ILogger<ApprovalStageAppService> logger)
        : base(approvalStageRepository)
    {
        _approvalStageRepository = approvalStageRepository;
        _approvalTemplateRepository = approvalTemplateRepository;
        _approvalCriteriaRepository = approvalCriteriaRepository;
        _approvalApproverRepository = approvalApproverRepository;
        _jettyRequestItemRepository = jettyRequestItemRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public override async Task<ApprovalStageDto> CreateAsync(CreateUpdateApprovalStageDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _approvalStageRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<ApprovalStageDto> UpdateAsync(Guid id, CreateUpdateApprovalStageDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalStageRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalStageRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalStageRepository.GetAsync(id);

        await _approvalStageRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<ApprovalStageDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalStageRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<ApprovalStageDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalStageRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalStageDto>(totalCount, dtos);
    }

    public virtual async Task<PagedResultDto<ApprovalStageDto>> FilterListAsync(QueryParametersDto parameters)
    {
        // Get the base query with approval template relationship
        var query = await _approvalStageRepository.WithDetailsAsync(x => x.ApprovalTemplate);

        // Apply filters and sorting
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Get the approval stages
        var approvalStages = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );

        // Manually load related entities for better data access as requested:
        // - JettyRequestItem with header (JettyRequest)
        // - User relationships for ApproverId and RequesterId

        var documentIds = approvalStages
            .Where(x => !string.IsNullOrEmpty(x.DocumentId) && Guid.TryParse(x.DocumentId, out _))
            .Select(x => Guid.Parse(x.DocumentId!))
            .Distinct()
            .ToList();

        // Load JettyRequestItems with their JettyRequest headers
        var jettyRequestItems = new Dictionary<Guid, JettyRequestItem>();
        if (documentIds.Any())
        {
            var items = await _jettyRequestItemRepository.WithDetailsAsync(x => x.JettyRequest);
            var filteredItems = items.Where(x => documentIds.Contains(x.Id)).ToList();
            jettyRequestItems = filteredItems.ToDictionary(x => x.Id, x => x);
        }

        // Set the navigation properties manually
        foreach (var stage in approvalStages)
        {
            if (!string.IsNullOrEmpty(stage.DocumentId) &&
                Guid.TryParse(stage.DocumentId, out var docId) &&
                jettyRequestItems.TryGetValue(docId, out var jettyRequestItem))
            {
                stage.JettyRequestItem = jettyRequestItem;
            }
        }

        var dtos = approvalStages.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<ApprovalStageDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<ApprovalStage> ApplyDynamicQuery(IQueryable<ApprovalStage> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ApprovalStage>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ApprovalStage>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }

    /// <summary>
    /// Submits a document for approval
    /// </summary>
    public async Task<List<ApprovalStageDto>> SubmitForApprovalAsync(SubmitApprovalDto input)
    {
        // _logger.LogInformation("Submitting document {DocumentId} of type {DocumentType} for approval",
        //     input.DocumentId, input.DocumentType);

        // 1. Get the JettyRequestItem to validate it exists
        var jettyRequestItem = await _jettyRequestItemRepository.GetAsync(input.DocumentId);
        if (jettyRequestItem == null)
        {
            throw new UserFriendlyException($"JettyRequestItem with ID {input.DocumentId} not found");
        }

        // 2. Find the approval template based on document type criteria
        var approvalCriterias = await _approvalCriteriaRepository.GetByDocumentTypeAsync(input.DocumentType);
        if (!approvalCriterias.Any())
        {
            throw new UserFriendlyException($"No approval template found for document type: {input.DocumentType}");
        }

        // Get the first matching approval template (you might want to add more sophisticated logic here)
        var approvalCriteria = approvalCriterias.First();
        var approvalTemplate = await _approvalTemplateRepository.GetAsync(approvalCriteria.ApprovalId);

        // 3. Get all approvers for this template, ordered by sequence
        var approvers = await _approvalApproverRepository.GetOrderedBySequenceAsync(approvalTemplate.Id);
        if (!approvers.Any())
        {
            throw new UserFriendlyException($"No approvers found for approval template: {approvalTemplate.Name}");
        }

        // 4. Create approval stages for each approver
        var approvalStages = new List<ApprovalStage>();
        var currentUserId = CurrentUser.Id?.ToString();
        var requestDate = Clock.Now;

        foreach (var approver in approvers)
        {
            var approvalStage = new ApprovalStage(
                GuidGenerator.Create(),
                approvalTemplate.Id,
                approver.ApproverId,
                actionDate: null,
                documentId: input.DocumentId.ToString(),
                requesterId: currentUserId,
                requestDate: requestDate,
                status: ApprovalStatus.Pending,
                notes: input.Notes
            );

            approvalStages.Add(approvalStage);
        }

        // 5. Save all approval stages
        await _approvalStageRepository.InsertManyAsync(approvalStages, autoSave: true);

        // Update JettyRequestItem status to Waiting (enum)
        jettyRequestItem.Status = JettyRequests.JettyRequestItemStatus.Waiting.ToString();
        await _jettyRequestItemRepository.UpdateAsync(jettyRequestItem, autoSave: true);

        // _logger.LogInformation("Created {Count} approval stages for document {DocumentId}",
        //     approvalStages.Count, input.DocumentId);

        // 6. Return the created approval stages as DTOs
        return approvalStages.Select(_mapper.MapToDto).ToList();
    }
}