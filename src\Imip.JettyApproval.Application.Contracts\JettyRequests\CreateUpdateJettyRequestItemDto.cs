using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.JettyApproval.JettyRequests;

/// <summary>
/// DTO for creating and updating JettyRequestItem entity
/// </summary>
public class CreateUpdateJettyRequestItemDto
{
    /// <summary>
    /// Foreign key to the parent jetty request
    /// </summary>
    public Guid? JettyRequestId { get; set; }

    /// <summary>
    /// Tenant name for the item
    /// </summary>
    [StringLength(100)]
    public string? TenantName { get; set; }

    /// <summary>
    /// Name of the item
    /// </summary>
    [StringLength(200)]
    public string? ItemName { get; set; }

    /// <summary>
    /// Quantity of the item
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal Qty { get; set; }

    /// <summary>
    /// Unit of measurement
    /// </summary>
    [StringLength(50)]
    public string? UoM { get; set; }

    /// <summary>
    /// Additional notes for the item
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }

    /// <summary>
    /// Status of the item
    /// </summary>
    [Required]
    public JettyRequestItemStatus Status { get; set; }

    /// <summary>
    /// Letter number for the item
    /// </summary>
    [StringLength(100)]
    public string? LetterNo { get; set; }

    /// <summary>
    /// Letter date for the item (formatted as yyyy-MM-dd)
    /// </summary>
    public string? LetterDate { get; set; }

    /// <summary>
    /// Reference ID for linking to attachments
    /// </summary>
    public Guid? ReferenceId { get; set; }
}

public enum JettyRequestItemStatus
{
    Draft,
    Open,
    Submit,
    Waiting,
    Approve,
    Reject
}