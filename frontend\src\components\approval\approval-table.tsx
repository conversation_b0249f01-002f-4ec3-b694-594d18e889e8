import { postApiIdjasApprovalStageFilterList } from "@/client/sdk.gen";
import type { ApprovalStageDto } from "@/client/types.gen";
import { Button } from "@/components/ui/button";
import { DocumentPreviewDialog } from "@/components/ui/DocumentPreviewDialog";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/lib/useToast";
import { useQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
} from "@tanstack/react-table";
import { ChevronLeft, ChevronRight, Search } from "lucide-react";
import React, { useState } from "react";
import ApprovalActions from "./approval-actions";

const columns: ColumnDef<ApprovalStageDto>[] = [
  {
    accessorKey: "approvalTemplate.name",
    header: "Template",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jettyRequestItem.itemName",
    header: "Item Name",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jettyRequestItem.jettyRequest.docNum",
    header: "Document Number",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jettyRequestItem.jettyRequest.vesselName",
    header: "Vessel Name",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jettyRequestItem.jettyRequest.vesselType",
    header: "Vessel Type",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "requestDate",
    header: "Request Date",
    cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : "-",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: info => {
      const status = info.getValue() as string | number;
      // Status mapping based on backend ApprovalStatus enum:
      // 0 = Pending, 1 = Approved, 2 = Rejected, 3 = Cancelled
      const statusText = status === 0 ? "Pending" :
                        status === 1 ? "Approved" :
                        status === 2 ? "Rejected" :
                        status === 3 ? "Cancelled" : "Unknown";
      const statusColor = status === 0 ? "text-yellow-600" : // Pending
                         status === 1 ? "text-green-600" : // Approved
                         status === 2 ? "text-red-600" : // Rejected
                         status === 3 ? "text-gray-600" : "text-gray-400"; // Cancelled or Unknown
      return <span className={statusColor}>{statusText}</span>;
    },
  },
  {
    accessorKey: "requesterUserName",
    header: "Requester",
    cell: info => info.getValue() ?? "-",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row, table }) => {
      const approval = row.original;
      // Fix the pending status condition - only status 0 is Pending (can be approved/rejected)
      const isPending = approval.status === 0;
      const tableMeta = table.options.meta as {
        setApprovalAction: (action: { isOpen: boolean; approvalId: string; action: "approve" | "reject" }) => void;
        handlePreview: (documentSrc: string) => void;
        getDocumentStreamUrl: (jettyRequestItemId: string) => string;
      };

      return (
        <div className="flex space-x-2">
          {isPending && (
            <>
              <Button
                variant="outline"
                size="sm"
                className="text-green-600 hover:text-green-700"
                onClick={() => {
                  tableMeta.setApprovalAction({
                    isOpen: true,
                    approvalId: approval.id || "",
                    action: "approve",
                  });
                }}
              >
                Approve
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-red-600 hover:text-red-700"
                onClick={() => {
                  tableMeta.setApprovalAction({
                    isOpen: true,
                    approvalId: approval.id || "",
                    action: "reject",
                  });
                }}
              >
                Reject
              </Button>
            </>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Use the new attachmentStreamUrls from the API response
              const streamUrls = approval.attachmentStreamUrls || [];
              if (streamUrls.length > 0) {
                // Use the first attachment stream URL
                const streamUrl = streamUrls[0];
                tableMeta.handlePreview(streamUrl);
              } else {
                // Fallback to the old method if no attachments
                const streamUrl = tableMeta.getDocumentStreamUrl(approval.documentId || "");
                if (streamUrl) {
                  tableMeta.handlePreview(streamUrl);
                }
              }
            }}
            disabled={!approval.attachmentStreamUrls?.length && !approval.documentId}
          >
            Preview
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // TODO: Implement view details action
              console.log("View details:", approval.id);
            }}
          >
            Details
          </Button>
        </div>
      );
    },
  },
];

const ApprovalTable: React.FC = () => {
  const { toast } = useToast();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // State for approval actions dialog
  const [approvalAction, setApprovalAction] = useState<{
    isOpen: boolean;
    approvalId: string;
    action: "approve" | "reject";
  }>({
    isOpen: false,
    approvalId: "",
    action: "approve",
  });

  // State for document preview dialog
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewDocumentSrc, setPreviewDocumentSrc] = useState("");

  // Helper function to handle document preview
  const handlePreview = (documentSrc: string) => {
    setPreviewDocumentSrc(documentSrc);
    setIsPreviewDialogOpen(true);
  };

  // Helper function to get document stream URL
  const getDocumentStreamUrl = (jettyRequestItemId: string) => {
    // Updated to match the backend URL pattern
    return `/api/attachment/stream/${jettyRequestItemId}`;
  };

  // Fetch approval stages with pending status for current user
  const { data: approvals = [], isLoading, error } = useQuery({
    queryKey: ["approval-stages", pagination.pageIndex, pagination.pageSize, globalFilter],
    queryFn: async () => {
      try {
        const response = await postApiIdjasApprovalStageFilterList({
          body: {
            maxResultCount: pagination.pageSize,
            skipCount: pagination.pageIndex * pagination.pageSize,
            // Note: Add proper filtering here based on the actual QueryParametersDto structure
            // For now, we'll get all records and filter on the frontend
          }
        });

        if (response.error) {
          throw new Error(response.error.error?.message || "Failed to fetch approvals");
        }

        return response.data?.items || [];
      } catch (err) {
        console.error("Error fetching approvals:", err);
        toast({
          title: "Error",
          description: "Failed to load approval list",
          variant: "destructive",
        });
        return [];
      }
    },
    retry: 1,
  });

  const table = useReactTable({
    data: approvals,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
    },
    meta: {
      setApprovalAction,
      handlePreview,
      getDocumentStreamUrl,
    },
  });

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-2">Error loading approvals</p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Pending Approvals</h2>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search approvals..."
              value={globalFilter ?? ""}
              onChange={(event) => setGlobalFilter(String(event.target.value))}
              className="pl-8 w-64"
            />
          </div>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  Loading approvals...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No pending approvals found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{" "}
          {Math.min(
            (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
            approvals.length
          )}{" "}
          of {approvals.length} results
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <ApprovalActions
        approvalId={approvalAction.approvalId}
        isOpen={approvalAction.isOpen}
        onClose={() => setApprovalAction({ ...approvalAction, isOpen: false })}
        action={approvalAction.action}
        onSuccess={() => {
          // Refresh the data after successful action
          // window.location.reload();
        }}
      />

      <DocumentPreviewDialog
        isOpen={isPreviewDialogOpen}
        onOpenChange={setIsPreviewDialogOpen}
        documentSrc={previewDocumentSrc}
      />
    </div>
  );
};

export default ApprovalTable;
