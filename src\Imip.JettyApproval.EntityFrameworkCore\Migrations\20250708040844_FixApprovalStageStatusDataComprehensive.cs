using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.JettyApproval.Migrations
{
    /// <inheritdoc />
    public partial class FixApprovalStageStatusDataComprehensive : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Comprehensive fix for Status column data type issues
            // Break into separate SQL statements to avoid batch execution issues

            // Step 1: Create a temporary column to store the corrected values
            migrationBuilder.Sql("ALTER TABLE ApprovalStages ADD StatusTemp INT;");

            // Step 2: Update the temporary column with proper integer values
            migrationBuilder.Sql(@"
                UPDATE ApprovalStages
                SET StatusTemp = CASE
                    -- Handle numeric strings
                    WHEN ISNUMERIC(Status) = 1 THEN CAST(Status AS INT)
                    -- Handle text values
                    WHEN Status = 'Draft' OR Status = 'Open' THEN 0
                    WHEN Status = 'Pending' OR Status = 'Waiting' THEN 1
                    WHEN Status = 'Approved' THEN 2
                    WHEN Status = 'Rejected' THEN 3
                    WHEN Status = 'Cancelled' THEN 4
                    -- Default to Pending for any unknown values
                    ELSE 1
                END;
            ");

            // Step 3: Drop the original Status column
            migrationBuilder.Sql("ALTER TABLE ApprovalStages DROP COLUMN Status;");

            // Step 4: Rename the temporary column to Status
            migrationBuilder.Sql("EXEC sp_rename 'ApprovalStages.StatusTemp', 'Status', 'COLUMN';");

            // Step 5: Make the column NOT NULL with default value
            migrationBuilder.Sql("ALTER TABLE ApprovalStages ALTER COLUMN Status INT NOT NULL;");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
