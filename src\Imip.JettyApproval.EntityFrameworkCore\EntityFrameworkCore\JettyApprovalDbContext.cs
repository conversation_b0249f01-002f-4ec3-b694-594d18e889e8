using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Imip.JettyApproval.Approvals.ApprovalDelegations;
using Imip.JettyApproval.Approvals.ApprovalStages;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Imip.JettyApproval.Attachments;
using Imip.JettyApproval.DocumentTemplates;
using Imip.JettyApproval.EntityTypeConfigurations;
using Imip.JettyApproval.JettyRequests;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement;
using Volo.Abp.TenantManagement.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore;

[ReplaceDbContext(typeof(IIdentityDbContext))]
[ReplaceDbContext(typeof(ITenantManagementDbContext))]
[ConnectionStringName("Default")]
public class JettyApprovalDbContext :
    AbpDbContext<JettyApprovalDbContext>,
    ITenantManagementDbContext,
    IIdentityDbContext
{
  #region Entities from the modules

  /* Notice: We only implemented IIdentityProDbContext and ISaasDbContext
   * and replaced them for this DbContext. This allows you to perform JOIN
   * queries for the entities of these modules over the repositories easily. You
   * typically don't need that for other modules. But, if you need, you can
   * implement the DbContext interface of the needed module and use ReplaceDbContext
   * attribute just like IIdentityProDbContext and ISaasDbContext.
   *
   * More info: Replacing a DbContext of a module ensures that the related module
   * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
   */

  // Identity
  public DbSet<IdentityUser> Users { get; set; }
  public DbSet<IdentityRole> Roles { get; set; }
  public DbSet<IdentityClaimType> ClaimTypes { get; set; }
  public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
  public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
  public DbSet<IdentityLinkUser> LinkUsers { get; set; }
  public DbSet<IdentityUserDelegation> UserDelegations { get; set; }
  public DbSet<IdentitySession> Sessions { get; set; }

  // Tenant Management
  public DbSet<Tenant> Tenants { get; set; }
  public DbSet<TenantConnectionString> TenantConnectionStrings { get; set; }

  #endregion

  public JettyApprovalDbContext(DbContextOptions<JettyApprovalDbContext> options)
      : base(options)
  {
  }

  protected override void OnModelCreating(ModelBuilder builder)
  {
    base.OnModelCreating(builder);

    /* Include modules to your migration db context */

    builder.ConfigurePermissionManagement();
    builder.ConfigureSettingManagement();
    builder.ConfigureBackgroundJobs();
    builder.ConfigureAuditLogging();
    builder.ConfigureFeatureManagement();
    builder.ConfigureIdentity();
    builder.ConfigureOpenIddict();
    builder.ConfigureTenantManagement();
    builder.ConfigureBlobStoring();

    /* Configure your own tables/entities inside here */

    #region SilkierQuartz
    var prefix = "Quartz";
    var schema = "Silkier";

    builder.ApplyConfiguration(
      new SilkierQuartzExecutionHistoryEntityTypeConfiguration(prefix, schema));

    builder.ApplyConfiguration(
      new SilkierQuartzJobSummaryEntityTypeConfiguration(prefix, schema));
    #endregion

    #region Quartz
    var quartzPrefix = "QRTZ_";
    var quartzSchema = "Quartz";

    builder.ApplyConfiguration(
      new QuartzJobDetailEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzSimpleTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzSimplePropertyTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzCronTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzBlobTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzCalendarEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzPausedTriggerGroupEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzFiredTriggerEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzSchedulerStateEntityTypeConfiguration(quartzPrefix, quartzSchema));

    builder.ApplyConfiguration(
      new QuartzLockEntityTypeConfiguration(quartzPrefix, quartzSchema));
    #endregion


    builder.Entity<ApprovalCriteria>(b =>
    {
      b.ToTable($"ApprovalCriterias", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();
      b.HasIndex(x => x.ApprovalId);

      // Configure foreign key relationship to use ApprovalId instead of ApprovalTemplateId
      b.HasOne(x => x.ApprovalTemplate)
        .WithMany(x => x.Criterias)
        .HasForeignKey(x => x.ApprovalId)
        .OnDelete(DeleteBehavior.Cascade);
    });

    builder.Entity<ApprovalApprover>(b =>
    {
      b.ToTable($"ApprovalApprovers", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();
      b.HasIndex(x => x.ApprovalId);
      b.HasIndex(x => x.ApproverId);

      // Configure foreign key relationship to use ApprovalId instead of ApprovalTemplateId
      b.HasOne(x => x.ApprovalTemplate)
        .WithMany(x => x.Approvers)
        .HasForeignKey(x => x.ApprovalId)
        .OnDelete(DeleteBehavior.Cascade);
    });

    builder.Entity<ApprovalStage>(b =>
    {
      b.ToTable($"ApprovalStages", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();
      b.HasIndex(x => x.ApprovalTemplateId);

      // Configure relationship to ApprovalTemplate
      b.HasOne(x => x.ApprovalTemplate)
        .WithMany(x => x.Stages)
        .HasForeignKey(x => x.ApprovalTemplateId)
        .OnDelete(DeleteBehavior.Cascade);

      // Explicitly ignore interface properties that can't be mapped to database
      b.Ignore(x => x.Approver);
      b.Ignore(x => x.Requester);

      // Ignore JettyRequestItem navigation property since we handle the relationship manually
      // using DocumentId string property instead of a foreign key
      b.Ignore(x => x.JettyRequestItem);

      // Configure Status enum to be stored as integer
      b.Property(x => x.Status)
        .HasConversion<int>();

      // Note: User relationships (Approver, Requester) are handled by ABP's user system
      // and don't need explicit FK constraints since they reference external user store
    });

    builder.Entity<ApprovalTemplate>(b =>
    {
      b.ToTable($"ApprovalTemplates", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();
    });

    builder.Entity<ApprovalDelegation>(b =>
    {
      b.ToTable($"ApprovalDelegations", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();
      b.HasIndex(x => x.ApproverId);
      b.HasIndex(x => x.SubstituteId);

      // Configure relationships using navigation properties
      // The [ForeignKey] attributes in the entity class will handle the FK mapping
      // Make relationships optional to handle IdentityUser global query filter
      b.HasOne(x => x.Approver)
          .WithMany()
          .HasForeignKey(x => x.ApproverId)
          .OnDelete(DeleteBehavior.NoAction)
          .IsRequired(false);

      b.HasOne(x => x.Substitute)
          .WithMany()
          .HasForeignKey(x => x.SubstituteId)
          .OnDelete(DeleteBehavior.NoAction)
          .IsRequired(false);
    });

    builder.Entity<JettyRequest>(b =>
    {
      b.ToTable($"JettyRequests", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();
      b.HasIndex(x => x.DocNum).IsUnique();
    });

    builder.Entity<JettyRequestItem>(b =>
    {
      b.ToTable($"JettyRequestItems", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();
      b.HasIndex(x => x.JettyRequestId);

      // Configure decimal precision for Qty property
      b.Property(x => x.Qty)
          .HasPrecision(18, 4); // 18 total digits, 4 decimal places
    });

    builder.Entity<Attachment>(b =>
    {
      b.ToTable($"Attachments", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();

      b.HasIndex(x => x.ReferenceId);
      b.HasIndex(x => x.ReferenceType);
    });

    builder.Entity<DocumentTemplate>(b =>
    {
      b.ToTable($"DocumentTemplates", JettyApprovalConsts.DbSchema);
      b.ConfigureByConvention();

      b.HasIndex(x => x.AttachmentId);
      b.HasIndex(x => x.DocumentType);
    });
  }
}
